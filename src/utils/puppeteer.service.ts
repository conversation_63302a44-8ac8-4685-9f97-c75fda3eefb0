import { Injectable, OnModuleD<PERSON>roy } from '@nestjs/common';
import puppeteer, { <PERSON>rows<PERSON> } from 'puppeteer';

export interface FooterOptions {
  companyPhone?: string;
  companyWebsite?: string;
  showPageNumbers?: boolean;
  customFooterContent?: string;
}

@Injectable()
export class PuppeteerService implements OnModuleDestroy {
  private browser: Browser | null = null;

  /**
   * Generate custom footer template with dynamic content
   */
  private generateFooterTemplate(
    options: FooterOptions = { showPageNumbers: false },
  ): string {
    const {
      companyPhone = '************',
      companyWebsite = 'www.lumigotransport.ca',
      showPageNumbers = true,
      customFooterContent,
    } = options;

    if (customFooterContent) {
      return customFooterContent;
    }

    return `
      <div style="font-size: 9px; padding: 5px 20px; width: 100%; display: flex; justify-content: space-between; align-items: center; border-top: 1px solid #e3e5f6; background: white; color: #667085;">
        <div>Phone: ${companyPhone} | ${companyWebsite}</div>
        ${showPageNumbers ? '<div>Page <span class="pageNumber"></span> of <span class="totalPages"></span></div>' : ''}
      </div>
    `;
  }

  async initBrowser(): Promise<Browser> {
    if (!this.browser) {
      this.browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu',
        ],
      });
    }
    return this.browser;
  }

  async closeBrowser(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }

  async generatePDFFromHTML(
    htmlContent: string,
    options?: any,
    footerOptions: FooterOptions = { showPageNumbers: false },
  ): Promise<Buffer> {
    const browser = await this.initBrowser();
    const page = await browser.newPage();
    try {
      // Set viewport for consistent rendering
      await page.setViewport({ width: 1200, height: 800 });

      // Use 'domcontentloaded' for faster rendering since we have inline CSS
      await page.setContent(htmlContent, {
        waitUntil: 'domcontentloaded',
        timeout: 10000,
      });

      await page.evaluateHandle('document.fonts.ready');
      await page.evaluate(() => {
        return Promise.all(
          Array.from(document.images).map((img) => {
            if (img.complete) return Promise.resolve();
            return new Promise((resolve) => {
              img.onload = resolve;
              img.onerror = resolve;
            });
          }),
        );
      });

      // Wait a bit for fonts to render properly
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Generate footer template only
      const footerTemplate = this.generateFooterTemplate(footerOptions);

      // Merge default options with provided options
      const pdfOptions = {
        format: 'A4',
        printBackground: true,
        margin: {
          top: '20px', // Reduced since no header
          right: '20px',
          bottom: '20px',
          left: '20px',
        },
        displayHeaderFooter: true,
        footerTemplate: footerTemplate,
        ...options, // Allow overriding defaults
      };

      const pdfBuffer = await page.pdf(pdfOptions);
      return Buffer.from(pdfBuffer);
    } finally {
      await page.close();
    }
  }

  async onModuleDestroy() {
    await this.closeBrowser();
  }
}
